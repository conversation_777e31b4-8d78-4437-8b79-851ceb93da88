import { ApiRequest, ApiResponse, ApiEnvironment, HttpMethod } from '../../../types';

/**
 * Helper function to create a new request
 */
export const createNewRequest = (): ApiRequest => ({
  id: `req_${Date.now()}`,
  name: 'New Request',
  method: 'GET',
  url: '',
  headers: [],
  params: [],
  body: {
    mode: 'none',
    enabled: true,
  },
  preRequestScript: '',
  testScript: '',
});

/**
 * Helper function to create a new environment
 */
export const createNewEnvironment = (): ApiEnvironment => ({
  id: `env_${Date.now()}`,
  name: 'New Environment',
  variables: [],
});

/**
 * Helper function to get color for HTTP method
 */
export const getMethodColor = (method: HttpMethod): string => {
  const colors: Record<HttpMethod, string> = {
    GET: '#61affe',
    POST: '#49cc90',
    PUT: '#fca130',
    DELETE: '#f93e3e',
    PATCH: '#50e3c2',
    HEAD: '#9012fe',
    OPTIONS: '#0d5aa7',
  };

  return colors[method] || '#6c757d';
};

/**
 * Helper function to resolve environment variables in a string
 */
export const resolveVariables = (
  text: string,
  environment?: ApiEnvironment
): string => {
  if (!text || !environment) {
    return text;
  }

  let resolvedText = text;

  // Replace {{variableName}} with the variable value
  environment.variables.forEach(variable => {
    if (variable.enabled) {
      const regex = new RegExp(`{{${variable.key}}}`, 'g');
      resolvedText = resolvedText.replace(regex, variable.value);
    }
  });

  return resolvedText;
};

/**
 * Function to send a request directly without using the Backstage proxy
 */
export const sendRequest = async (
  request: ApiRequest,
  environment?: ApiEnvironment
): Promise<ApiResponse> => {
  try {
    // Resolve environment variables in URL
    const resolvedUrl = resolveVariables(request.url, environment);

    // Validate the URL
    let url: URL;
    try {
      url = new URL(resolvedUrl);
    } catch (error) {
      throw new Error(`Invalid URL: ${resolvedUrl}. Please provide a valid URL including protocol (e.g., https://)`);
    }

    // Process headers with environment variables
    const headers: Record<string, string> = {};
    request.headers
      .filter(header => header.enabled)
      .forEach(header => {
        const key = resolveVariables(header.key, environment);
        const value = resolveVariables(header.value, environment);
        headers[key] = value;
      });

    // Process URL params with environment variables
    const params = new URLSearchParams(url.search);
    
    // Add params from the request
    request.params
      .filter(param => param.enabled)
      .forEach(param => {
        const key = resolveVariables(param.key, environment);
        const value = resolveVariables(param.value, environment);
        params.append(key, value);
      });

    // Update the URL with the processed params
    url.search = params.toString();

    // Process request body with environment variables
    let processedBody: string | FormData | URLSearchParams | null = null;

    // Only process body if it's enabled (default to true if not specified)
    const bodyEnabled = request.body.enabled !== false;
    
    if (bodyEnabled) {
      if (request.body.mode === 'raw' && request.body.raw) {
        processedBody = resolveVariables(request.body.raw, environment);
      } else if (request.body.mode === 'urlencoded' && request.body.urlencoded) {
        const urlencoded = new URLSearchParams();
        request.body.urlencoded.forEach(item => {
          const key = resolveVariables(item.key, environment);
          const value = resolveVariables(item.value, environment);
          urlencoded.append(key, value);
        });
        processedBody = urlencoded;
      } else if (request.body.mode === 'form-data' && request.body.formData) {
        const formData = new FormData();
        request.body.formData.forEach(item => {
          const key = resolveVariables(item.key, environment);
          const value = resolveVariables(item.value, environment);
          formData.append(key, value);
        });
        processedBody = formData;
      }
    }

    // Prepare the fetch options
    const fetchOptions: RequestInit = {
      method: request.method,
      headers: headers,
      mode: 'cors', // Enable CORS for cross-origin requests
    };

    // Add the body if it's not a GET or HEAD request
    if (request.method !== 'GET' && request.method !== 'HEAD' && processedBody) {
      fetchOptions.body = processedBody;
    }

    // Record the start time
    const startTime = performance.now();

    // Send the request directly to the target URL
    const response = await fetch(url.toString(), fetchOptions);

    // Record the end time
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    // Get the response headers
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });

    // Get the response body
    let responseBody: string;
    try {
      responseBody = await response.text();
    } catch (error) {
      responseBody = 'Failed to read response body';
    }

    return {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
      body: responseBody,
      time: responseTime,
    };
  } catch (error) {
    throw error instanceof Error 
      ? error 
      : new Error(`Request failed: ${String(error)}`);
  }
};

/**
 * Helper function to get URL helper text
 */
export const getUrlHelperText = (url: string): string => {
  if (!url) {
    return "Enter a URL to send a request";
  }

  // Direct request without proxy
  return "Request will be sent directly to the target URL";
};
