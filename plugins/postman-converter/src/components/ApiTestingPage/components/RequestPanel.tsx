import React from 'react';
import {
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Button,
  Box,
  Tabs,
  Tab,
  CircularProgress,
  makeStyles,
} from '@material-ui/core';
import SendIcon from '@material-ui/icons/Send';
import { ApiRequest, ApiEnvironment, HttpMethod } from '../../../types';
import { getUrlHelperText } from '../utils/requestUtils';
import { TabPanel } from './TabPanel';
import { ParamsTab } from './ParamsTab';
import { HeadersTab } from './HeadersTab';
import { BodyTab } from './BodyTab';
import { AuthTab } from './AuthTab';
import { PreRequestScriptPanel } from '../PreRequestScriptPanel';
import { TestGeneratorPanel } from '../TestGeneratorPanel';
import { TestResultsPanel } from '../TestResultsPanel';
import { Alert } from '@material-ui/lab';

const useStyles = makeStyles(theme => ({
  urlBar: {
    display: 'flex',
    marginBottom: theme.spacing(2),
    alignItems: 'flex-start',
  },
  methodSelect: {
    minWidth: 120,
    marginRight: theme.spacing(1),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  tabContent: {
    padding: theme.spacing(2),
    minHeight: '200px',
  },
}));

interface RequestPanelProps {
  request: ApiRequest;
  response: any;
  isLoading: boolean;
  tabValue: number;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onMethodChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  onUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSendRequest: () => void;
  onUpdateRequest: (updatedRequest: ApiRequest) => void;
  // Test related props
  testResults: any[];
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  testError: string | null;
  onGenerateTests: () => void;
  onRunTests: () => void;
  onSaveTests: (testScript: string) => void;
  // Pre-request script props
  isSavingPreRequestScript: boolean;
  preRequestScriptError: string | null;
  onSavePreRequestScript: (script: string) => void;
  // Environment
  currentEnvironment?: ApiEnvironment;
}

export const RequestPanel: React.FC<RequestPanelProps> = ({
  request,
  response,
  isLoading,
  tabValue,
  onTabChange,
  onMethodChange,
  onUrlChange,
  onSendRequest,
  onUpdateRequest,
  testResults,
  isGeneratingTests,
  isRunningTests,
  testError,
  onGenerateTests,
  onRunTests,
  onSaveTests,
  isSavingPreRequestScript,
  preRequestScriptError,
  onSavePreRequestScript,
  currentEnvironment,
}) => {
  const classes = useStyles();

  const handleParamsChange = (params: { key: string; value: string; enabled: boolean }[]) => {
    onUpdateRequest({
      ...request,
      params,
    });
  };

  const handleHeadersChange = (headers: { key: string; value: string; enabled: boolean }[]) => {
    onUpdateRequest({
      ...request,
      headers,
    });
  };

  const handleBodyChange = (body: ApiRequest['body']) => {
    onUpdateRequest({
      ...request,
      body,
    });
  };

  const handleAuthChange = (auth: ApiRequest['auth']) => {
    onUpdateRequest({
      ...request,
      auth,
    });
  };

  return (
    <>
      {/* Request URL bar */}
      <div className={classes.urlBar}>
        <FormControl className={classes.methodSelect}>
          <Select
            value={request.method}
            onChange={onMethodChange}
            variant="outlined"
          >
            <MenuItem value="GET">GET</MenuItem>
            <MenuItem value="POST">POST</MenuItem>
            <MenuItem value="PUT">PUT</MenuItem>
            <MenuItem value="DELETE">DELETE</MenuItem>
            <MenuItem value="PATCH">PATCH</MenuItem>
            <MenuItem value="HEAD">HEAD</MenuItem>
            <MenuItem value="OPTIONS">OPTIONS</MenuItem>
          </Select>
        </FormControl>
        <TextField
          className={classes.urlField}
          variant="outlined"
          size="small"
          placeholder="Enter request URL"
          value={request.url}
          onChange={onUrlChange}
          helperText={getUrlHelperText(request.url)}
        />
        <Button
          variant="contained"
          color="primary"
          startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={onSendRequest}
          disabled={isLoading || !request.url}
        >
          Send
        </Button>
      </div>

      {/* Request tabs */}
      <Tabs
        value={tabValue}
        onChange={onTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab label="Params" />
        <Tab label="Headers" />
        <Tab label="Body" />
        <Tab label="Auth" />
        <Tab label="Pre-request" />
        <Tab label="Tests" />
      </Tabs>

      {/* Params tab */}
      <TabPanel value={tabValue} index={0}>
        <ParamsTab
          params={request.params}
          onChange={handleParamsChange}
        />
      </TabPanel>

      {/* Headers tab */}
      <TabPanel value={tabValue} index={1}>
        <HeadersTab
          headers={request.headers}
          onChange={handleHeadersChange}
        />
      </TabPanel>

      {/* Body tab */}
      <TabPanel value={tabValue} index={2}>
        <BodyTab
          body={request.body}
          onChange={handleBodyChange}
        />
      </TabPanel>

      {/* Auth tab */}
      <TabPanel value={tabValue} index={3}>
        <AuthTab
          auth={request.auth}
          onChange={handleAuthChange}
        />
      </TabPanel>

      {/* Pre-request script tab */}
      <TabPanel value={tabValue} index={4}>
        <Box display="flex" flexDirection="column">
          <PreRequestScriptPanel
            request={request}
            onSaveScript={onSavePreRequestScript}
            isSaving={isSavingPreRequestScript}
            error={preRequestScriptError}
          />
        </Box>
      </TabPanel>

      {/* Tests tab */}
      <TabPanel value={tabValue} index={5}>
        <Box display="flex" flexDirection="column">
          <Box display="flex" flexDirection="row" flexWrap="wrap">
            <Box flex="1" minWidth="300px" marginRight={2}>
              <TestGeneratorPanel
                request={request}
                response={response}
                onRunTests={onRunTests}
                onSaveTests={onSaveTests}
                isGenerating={isGeneratingTests}
                isRunning={isRunningTests}
                error={testError}
              />
            </Box>
            <Box flex="1" minWidth="300px">
              <TestResultsPanel
                results={testResults}
                isRunning={isRunningTests}
                error={testError}
              />
            </Box>
          </Box>

          {!response && (
            <Box mt={2}>
              <Alert severity="info">
                Send a request to generate tests based on the response.
              </Alert>
            </Box>
          )}

          {response && !request.testScript && (
            <Box mt={2} display="flex" justifyContent="center">
              <Button
                variant="contained"
                color="primary"
                onClick={onGenerateTests}
                disabled={isGeneratingTests}
                startIcon={isGeneratingTests ? <CircularProgress size={20} /> : null}
              >
                {isGeneratingTests ? 'Generating...' : 'Generate Tests from Response'}
              </Button>
            </Box>
          )}
        </Box>
      </TabPanel>
    </>
  );
};
